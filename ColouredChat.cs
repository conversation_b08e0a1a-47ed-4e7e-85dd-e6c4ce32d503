using System;
using System.Collections.Generic;
using System.Linq;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Oxide.Game.Rust.Cui;
using UnityEngine;
using Newtonsoft.Json;

namespace Oxide.Plugins
{
    [Info("ColouredChat", "YourName", "2.0.0")]
    [Description("Enhanced chat coloring system with UI for player color selection")]
    public class ColouredChat : RustPlugin
    {
        #region Configuration
        private Configuration config;

        public class Configuration
        {
            [JsonProperty("Available Colors")]
            public Dictionary<string, string> AvailableColors = new Dictionary<string, string>
            {
                ["White"] = "#FFFFFF",
                ["Red"] = "#FF0000",
                ["Green"] = "#00FF00",
                ["Blue"] = "#0000FF",
                ["Yellow"] = "#FFFF00",
                ["Orange"] = "#FFA500",
                ["Purple"] = "#800080",
                ["Pink"] = "#FFC0CB",
                ["Cyan"] = "#00FFFF",
                ["Lime"] = "#32CD32",
                ["Gold"] = "#FFD700",
                ["Silver"] = "#C0C0C0"
            };

            [JsonProperty("Default Color")]
            public string DefaultColor = "#FFFFFF";

            [JsonProperty("VIP Colors (Permission Required)")]
            public Dictionary<string, string> VipColors = new Dictionary<string, string>
            {
                ["Rainbow"] = "rainbow",
                ["Gradient"] = "gradient",
                ["Diamond"] = "#B9F2FF",
                ["Emerald"] = "#50C878"
            };

            [JsonProperty("UI Settings")]
            public UISettings UI = new UISettings();

            [JsonProperty("Chat Format")]
            public string ChatFormat = "<color={color}>{username}</color>: {message}";

            [JsonProperty("Enable Permission System")]
            public bool EnablePermissions = true;
        }

        public class UISettings
        {
            [JsonProperty("Background Color")]
            public string BackgroundColor = "0.1 0.1 0.1 0.8";

            [JsonProperty("Button Color")]
            public string ButtonColor = "0.2 0.2 0.2 0.8";

            [JsonProperty("Text Color")]
            public string TextColor = "1 1 1 1";

            [JsonProperty("UI Anchor Min")]
            public string AnchorMin = "0.3 0.2";

            [JsonProperty("UI Anchor Max")]
            public string AnchorMax = "0.7 0.8";
        }

        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null)
                {
                    throw new JsonException();
                }
            }
            catch
            {
                LoadDefaultConfig();
                PrintWarning("Configuration file is corrupt, using default values");
            }
        }

        protected override void SaveConfig()
        {
            Config.WriteObject(config);
        }
        #endregion

        #region Data Storage
        private Dictionary<ulong, string> playerColors = new Dictionary<ulong, string>();
        private const string DataFileName = "ColouredChatData";

        private void SaveData()
        {
            Interface.Oxide.DataFileSystem.WriteObject(DataFileName, playerColors);
        }

        private void LoadData()
        {
            try
            {
                playerColors = Interface.Oxide.DataFileSystem.ReadObject<Dictionary<ulong, string>>(DataFileName);
            }
            catch
            {
                playerColors = new Dictionary<ulong, string>();
            }
        }
        #endregion

        #region Hooks
        void Init()
        {
            LoadData();

            // Register permissions
            permission.RegisterPermission("colouredchat.vip", this);
            permission.RegisterPermission("colouredchat.admin", this);

            PrintWarning("ColouredChat plugin loaded successfully!");
        }

        void OnServerInitialized()
        {
            // Ensure config is loaded
            if (config == null)
            {
                LoadDefaultConfig();
                SaveConfig();
            }
        }

        void Unload()
        {
            SaveData();
            
            // Close all UIs
            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "ColouredChatUI");
            }
        }

        object OnUserChat(IPlayer player, string message)
        {
            var basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return null;

            var userId = basePlayer.userID;
            var playerColor = playerColors.ContainsKey(userId) ? playerColors[userId] : config.DefaultColor;
            
            // Handle special effects
            if (playerColor == "rainbow")
            {
                if (HasPermission(basePlayer, "colouredchat.vip"))
                {
                    var rainbowMessage = CreateRainbowText(basePlayer.displayName);
                    var formattedMessage = config.ChatFormat
                        .Replace("{color}", "#FFFFFF")
                        .Replace("{username}", rainbowMessage)
                        .Replace("{message}", message);
                    
                    Server.Broadcast(formattedMessage);
                    return false; // Cancel original message
                }
            }
            else if (playerColor == "gradient")
            {
                if (HasPermission(basePlayer, "colouredchat.vip"))
                {
                    var gradientMessage = CreateGradientText(basePlayer.displayName);
                    var formattedMessage = config.ChatFormat
                        .Replace("{color}", "#FFFFFF")
                        .Replace("{username}", gradientMessage)
                        .Replace("{message}", message);
                    
                    Server.Broadcast(formattedMessage);
                    return false; // Cancel original message
                }
            }

            // Regular color formatting
            var coloredMessage = config.ChatFormat
                .Replace("{color}", playerColor)
                .Replace("{username}", basePlayer.displayName)
                .Replace("{message}", message);

            Server.Broadcast(coloredMessage);
            return false; // Cancel original message to use our formatting
        }
        #endregion

        #endregion

        #region Commands
        [ChatCommand("color")]
        void ColorCommand(BasePlayer player, string command, string[] args)
        {
            if (player == null) return;

            if (args.Length == 0)
            {
                ShowColorUI(player);
                return;
            }

            if (args[0].ToLower() == "reset")
            {
                playerColors[player.userID] = config.DefaultColor;
                SaveData();
                SendReply(player, "Your chat color has been reset to default.");
                return;
            }

            // Direct color setting (admin only)
            if (HasPermission(player, "colouredchat.admin") && args.Length >= 2)
            {
                var targetPlayer = BasePlayer.Find(args[0]);
                if (targetPlayer != null && args[1].StartsWith("#"))
                {
                    playerColors[targetPlayer.userID] = args[1];
                    SaveData();
                    SendReply(player, $"Set {targetPlayer.displayName}'s color to {args[1]}");
                    return;
                }
            }

            // Show help message for invalid usage
            SendReply(player, "Usage: /color - Opens color selection UI");
            SendReply(player, "       /color reset - Resets to default color");
            if (HasPermission(player, "colouredchat.admin"))
            {
                SendReply(player, "       /color <player> <#hexcolor> - Set player's color (admin)");
            }
        }

        [ChatCommand("colors")]
        void ColorsCommand(BasePlayer player, string command, string[] args)
        {
            ColorCommand(player, command, args);
        }

        [ChatCommand("colours")]
        void ColoursCommand(BasePlayer player, string command, string[] args)
        {
            ColorCommand(player, command, args);
        }
        #endregion

        #region UI System
        void ShowColorUI(BasePlayer player)
        {
            var elements = new CuiElementContainer();

            // Main panel
            elements.Add(new CuiPanel
            {
                Image = { Color = config.UI.BackgroundColor },
                RectTransform = { AnchorMin = config.UI.AnchorMin, AnchorMax = config.UI.AnchorMax },
                CursorEnabled = true
            }, "Overlay", "ColouredChatUI");

            // Title
            elements.Add(new CuiLabel
            {
                Text = { Text = "Choose Your Chat Color", FontSize = 20, Align = TextAnchor.MiddleCenter, Color = config.UI.TextColor },
                RectTransform = { AnchorMin = "0 0.85", AnchorMax = "1 0.95" }
            }, "ColouredChatUI");

            // Close button
            elements.Add(new CuiButton
            {
                Button = { Command = "colorui.close", Color = "0.8 0.2 0.2 0.8" },
                RectTransform = { AnchorMin = "0.9 0.85", AnchorMax = "0.98 0.95" },
                Text = { Text = "X", FontSize = 14, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, "ColouredChatUI");

            // Current color display
            var currentColor = playerColors.ContainsKey(player.userID) ? playerColors[player.userID] : config.DefaultColor;
            elements.Add(new CuiLabel
            {
                Text = { Text = $"Current: <color={currentColor}>{player.displayName}</color>", FontSize = 14, Align = TextAnchor.MiddleCenter, Color = config.UI.TextColor },
                RectTransform = { AnchorMin = "0 0.75", AnchorMax = "1 0.82" }
            }, "ColouredChatUI");

            // Regular colors
            var regularColors = config.AvailableColors.ToList();
            var buttonsPerRow = 4;
            var rows = (int)Math.Ceiling((double)regularColors.Count / buttonsPerRow);

            for (int i = 0; i < regularColors.Count; i++)
            {
                var color = regularColors[i];
                var row = i / buttonsPerRow;
                var col = i % buttonsPerRow;

                var xMin = 0.05f + (col * 0.22f);
                var xMax = xMin + 0.2f;
                var yMax = 0.7f - (row * 0.12f);
                var yMin = yMax - 0.1f;

                elements.Add(new CuiButton
                {
                    Button = { Command = $"colorui.select {color.Value}", Color = config.UI.ButtonColor },
                    RectTransform = { AnchorMin = $"{xMin} {yMin}", AnchorMax = $"{xMax} {yMax}" },
                    Text = { Text = $"<color={color.Value}>{color.Key}</color>", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, "ColouredChatUI");
            }

            // VIP colors section (if player has permission)
            if (HasPermission(player, "colouredchat.vip") && config.VipColors.Count > 0)
            {
                var vipStartY = 0.7f - (rows * 0.12f) - 0.05f;

                elements.Add(new CuiLabel
                {
                    Text = { Text = "VIP Colors", FontSize = 16, Align = TextAnchor.MiddleCenter, Color = "#FFD700" },
                    RectTransform = { AnchorMin = $"0 {vipStartY - 0.05f}", AnchorMax = $"1 {vipStartY}" }
                }, "ColouredChatUI");

                var vipColors = config.VipColors.ToList();
                for (int i = 0; i < vipColors.Count; i++)
                {
                    var color = vipColors[i];
                    var col = i % buttonsPerRow;
                    var row = i / buttonsPerRow;

                    var xMin = 0.05f + (col * 0.22f);
                    var xMax = xMin + 0.2f;
                    var yMax = vipStartY - 0.1f - (row * 0.12f);
                    var yMin = yMax - 0.1f;

                    var displayText = color.Key;
                    if (color.Value == "rainbow")
                        displayText = CreateRainbowText(color.Key);
                    else if (color.Value == "gradient")
                        displayText = CreateGradientText(color.Key);
                    else
                        displayText = $"<color={color.Value}>{color.Key}</color>";

                    elements.Add(new CuiButton
                    {
                        Button = { Command = $"colorui.select {color.Value}", Color = config.UI.ButtonColor },
                        RectTransform = { AnchorMin = $"{xMin} {yMin}", AnchorMax = $"{xMax} {yMax}" },
                        Text = { Text = displayText, FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                    }, "ColouredChatUI");
                }
            }

            // Reset button
            elements.Add(new CuiButton
            {
                Button = { Command = "colorui.reset", Color = "0.6 0.6 0.6 0.8" },
                RectTransform = { AnchorMin = "0.05 0.05", AnchorMax = "0.25 0.12" },
                Text = { Text = "Reset to Default", FontSize = 10, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, "ColouredChatUI");

            CuiHelper.AddUi(player, elements);
        }
        #endregion

        #region Console Commands
        [ConsoleCommand("colorui.select")]
        void SelectColorCommand(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;

            var color = arg.GetString(0);
            if (string.IsNullOrEmpty(color)) return;

            // Check VIP permission for special colors
            if ((color == "rainbow" || color == "gradient" || config.VipColors.ContainsValue(color))
                && !HasPermission(player, "colouredchat.vip"))
            {
                SendReply(player, "You don't have permission to use VIP colors!");
                return;
            }

            playerColors[player.userID] = color;
            SaveData();

            var colorName = color;
            if (color.StartsWith("#"))
            {
                var colorEntry = config.AvailableColors.FirstOrDefault(x => x.Value == color);
                if (!colorEntry.Equals(default(KeyValuePair<string, string>)))
                    colorName = colorEntry.Key;
            }
            else if (config.VipColors.ContainsValue(color))
            {
                var vipEntry = config.VipColors.FirstOrDefault(x => x.Value == color);
                if (!vipEntry.Equals(default(KeyValuePair<string, string>)))
                    colorName = vipEntry.Key;
            }

            SendReply(player, $"Your chat color has been set to {colorName}!");
            CuiHelper.DestroyUi(player, "ColouredChatUI");
        }

        [ConsoleCommand("colorui.reset")]
        void ResetColorCommand(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;

            playerColors[player.userID] = config.DefaultColor;
            SaveData();
            SendReply(player, "Your chat color has been reset to default!");
            CuiHelper.DestroyUi(player, "ColouredChatUI");
        }

        [ConsoleCommand("colorui.close")]
        void CloseUICommand(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;

            CuiHelper.DestroyUi(player, "ColouredChatUI");
        }
        #endregion

        #region Helper Methods
        private bool HasPermission(BasePlayer player, string perm)
        {
            if (!config.EnablePermissions) return true;
            return permission.UserHasPermission(player.UserIDString, perm);
        }

        private string CreateRainbowText(string text)
        {
            var colors = new[] { "#FF0000", "#FF7F00", "#FFFF00", "#00FF00", "#0000FF", "#4B0082", "#9400D3" };
            var result = "";

            for (int i = 0; i < text.Length; i++)
            {
                var colorIndex = i % colors.Length;
                result += $"<color={colors[colorIndex]}>{text[i]}</color>";
            }

            return result;
        }

        private string CreateGradientText(string text)
        {
            var result = "";
            var length = text.Length;

            for (int i = 0; i < length; i++)
            {
                var ratio = (float)i / (length - 1);
                var r = (int)(255 * (1 - ratio) + 100 * ratio);
                var g = (int)(100 * (1 - ratio) + 255 * ratio);
                var b = 255;

                result += $"<color=#{r:X2}{g:X2}{b:X2}>{text[i]}</color>";
            }

            return result;
        }
        #endregion
    }
}
